# Institute Profile Frontend Integration Guide

This guide explains how to integrate the institute profile API with your frontend to provide a seamless user experience with dynamic button states and clear action guidance.

## Overview

The institute profile system has several states, and the frontend should adapt its UI based on the current state:

1. **Profile Not Created** - Show "Create Profile" button
2. **Profile Incomplete** - Show "Update Profile" button, disabled "Send for Approval"
3. **Profile Complete** - Show "Update Profile" and "Send for Approval" buttons
4. **Under Review** - Show disabled "Update Profile", no approval button
5. **Verified** - Show "Update Profile" button only
6. **Rejected** - Show "Update Profile" and "Resubmit for Approval" buttons

## API Endpoints

### 1. Get Profile Status (Recommended for UI State)

```http
GET /api/institutes/profile/status
Authorization: Bearer {token}
```

**Response Example:**
```json
{
  "profile_exists": true,
  "profile_complete": true,
  "verification_status": "pending",
  "is_verified": false,
  "can_submit_for_verification": true,
  "completion_percentage": 100.0,
  "missing_fields": [],
  "ui_actions": {
    "primary_button": {
      "text": "Update Profile",
      "action": "update_profile",
      "endpoint": "PUT /api/institutes/profile/with-documents",
      "enabled": true
    },
    "secondary_button": {
      "text": "Send for Approval",
      "action": "submit_for_verification",
      "endpoint": "POST /api/institutes/profile/submit-for-verification",
      "enabled": true,
      "style": "success"
    },
    "show_submit_for_approval": true,
    "status_message": "Your profile is complete and ready for verification!"
  }
}
```

### 2. Get Full Profile Data

```http
GET /api/institutes/profile
Authorization: Bearer {token}
```

Use this to populate form fields when editing.

### 3. Update Profile with Documents

```http
PUT /api/institutes/profile/with-documents
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

### 4. Submit for Verification

```http
POST /api/institutes/profile/submit-for-verification
Authorization: Bearer {token}
```

## Frontend Implementation

### React Example

```jsx
import React, { useState, useEffect } from 'react';

function InstituteProfileManager() {
  const [profileStatus, setProfileStatus] = useState(null);
  const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProfileStatus();
  }, []);

  const fetchProfileStatus = async () => {
    try {
      const response = await fetch('/api/institutes/profile/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });
      const status = await response.json();
      setProfileStatus(status);
      
      // If profile exists, fetch full data for form
      if (status.profile_exists) {
        fetchProfileData();
      }
    } catch (error) {
      console.error('Error fetching profile status:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProfileData = async () => {
    try {
      const response = await fetch('/api/institutes/profile', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setProfileData(data);
      }
    } catch (error) {
      console.error('Error fetching profile data:', error);
    }
  };

  const handlePrimaryAction = async () => {
    const action = profileStatus.ui_actions.primary_button.action;
    
    if (action === 'create_profile' || action === 'update_profile') {
      // Open profile form modal or navigate to profile form
      openProfileForm();
    }
  };

  const handleSecondaryAction = async () => {
    const action = profileStatus.ui_actions.secondary_button.action;
    
    if (action === 'submit_for_verification') {
      try {
        const response = await fetch('/api/institutes/profile/submit-for-verification', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        });
        
        if (response.ok) {
          alert('Profile submitted for verification successfully!');
          fetchProfileStatus(); // Refresh status
        } else {
          const error = await response.json();
          alert(`Error: ${error.detail}`);
        }
      } catch (error) {
        console.error('Error submitting for verification:', error);
        alert('Network error occurred');
      }
    }
  };

  if (loading) return <div>Loading...</div>;

  const { ui_actions } = profileStatus;

  return (
    <div className="institute-profile-manager">
      <h2>Institute Profile</h2>
      
      {/* Status Message */}
      {ui_actions.status_message && (
        <div className={`status-message ${profileStatus.verification_status}`}>
          {ui_actions.status_message}
        </div>
      )}

      {/* Progress Bar */}
      <div className="progress-bar">
        <div 
          className="progress-fill" 
          style={{ width: `${profileStatus.completion_percentage}%` }}
        />
        <span>{profileStatus.completion_percentage}% Complete</span>
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        {/* Primary Button */}
        <button
          onClick={handlePrimaryAction}
          disabled={!ui_actions.primary_button.enabled}
          className={`btn btn-primary ${!ui_actions.primary_button.enabled ? 'disabled' : ''}`}
          title={ui_actions.primary_button.disabled_reason}
        >
          {ui_actions.primary_button.text}
        </button>

        {/* Secondary Button */}
        {ui_actions.secondary_button && (
          <button
            onClick={handleSecondaryAction}
            disabled={!ui_actions.secondary_button.enabled}
            className={`btn btn-${ui_actions.secondary_button.style || 'secondary'} ${!ui_actions.secondary_button.enabled ? 'disabled' : ''}`}
            title={ui_actions.secondary_button.disabled_reason}
          >
            {ui_actions.secondary_button.text}
          </button>
        )}
      </div>

      {/* Missing Fields Alert */}
      {profileStatus.missing_fields.length > 0 && (
        <div className="missing-fields-alert">
          <h4>Required Fields Missing:</h4>
          <ul>
            {profileStatus.missing_fields.map(field => (
              <li key={field}>{field.replace('_', ' ').toUpperCase()}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

export default InstituteProfileManager;
```

### CSS Styles

```css
.institute-profile-manager {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.status-message {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.status-message.pending { background-color: #fff3cd; color: #856404; }
.status-message.verified { background-color: #d4edda; color: #155724; }
.status-message.rejected { background-color: #f8d7da; color: #721c24; }
.status-message.under_review { background-color: #d1ecf1; color: #0c5460; }

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 20px;
  position: relative;
}

.progress-fill {
  height: 100%;
  background-color: #28a745;
  transition: width 0.3s ease;
}

.progress-bar span {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-success { background-color: #28a745; color: white; }
.btn-warning { background-color: #ffc107; color: #212529; }

.btn:hover:not(.disabled) { opacity: 0.8; }
.btn.disabled { opacity: 0.5; cursor: not-allowed; }

.missing-fields-alert {
  background-color: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}
```

## Verification Status States

| Status | Description | Primary Button | Secondary Button |
|--------|-------------|----------------|------------------|
| `not_created` | No profile exists | "Create Profile" (enabled) | None |
| `pending` (incomplete) | Profile exists but missing fields | "Update Profile" (enabled) | "Send for Approval" (disabled) |
| `pending` (complete) | Profile complete, ready for submission | "Update Profile" (enabled) | "Send for Approval" (enabled) |
| `under_review` | Submitted and being reviewed | "Update Profile" (disabled) | None |
| `verified` | Approved and active | "Update Profile" (enabled) | None |
| `rejected` | Rejected, needs fixes | "Update Profile" (enabled) | "Resubmit for Approval" (enabled if complete) |

## Best Practices

1. **Always check profile status first** before rendering the UI
2. **Use the `ui_actions` object** to determine button states and text
3. **Show progress indicators** using `completion_percentage`
4. **Display helpful messages** using `status_message`
5. **Handle disabled states gracefully** with tooltips explaining why
6. **Refresh status after actions** to update the UI state
7. **Validate required fields** before enabling submission buttons

This approach ensures a consistent and user-friendly experience across different profile states.
