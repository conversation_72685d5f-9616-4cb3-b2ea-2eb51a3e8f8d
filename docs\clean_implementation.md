# Clean Implementation - Institute Endpoints

## Overview

The institute endpoints now follow clean architecture principles with proper separation of concerns.

## Architecture

### 1. Routes (HTTP Layer)
**File:** `app/Routes/Institute/Institute.py`

Routes handle only HTTP concerns:
- Request validation using Pydantic models
- Authentication and authorization
- Calling CRUD operations
- Returning responses

```python
@router.put("/profile", response_model=InstituteDetailedOut)
def update_my_institute_profile(
    profile_update: InstituteProfileUpdate,  # ← Pydantic model
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Update institute profile using clean JSON"""
    current_user = get_current_user(token, db)
    return update_institute_profile(db, current_user.id, profile_update)  # ← CRUD call
```

### 2. CRUD Operations (Business Logic Layer)
**File:** `app/Cruds/Institute/Institute.py`

CRUD operations handle business logic:
- Database operations
- File handling
- Business rules validation
- Transaction management

```python
async def update_institute_profile_with_documents(
    db: Session, 
    institute_id: uuid.UUID, 
    profile_update: InstituteProfileUpdate,
    document_files: List = None,
    document_metadata: List = None
) -> InstituteDetailedOut:
    """CRUD operation for profile updates with documents"""
    # Business logic here
    # File uploads, database operations, etc.
```

### 3. Schemas (Data Layer)
**File:** `app/Schemas/Institute/Institute.py`

Pydantic models define data structure and validation:

```python
class InstituteProfileUpdate(BaseModel):
    institute_name: Optional[str] = Field(None, min_length=2, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    # ... other fields with validation

class InstituteProfileWithDocumentsRequest(BaseModel):
    # Profile fields (inherits from above)
    institute_name: Optional[str] = Field(None, min_length=2, max_length=200)
    # ... other profile fields
    
    # Document metadata
    document_metadata: Optional[List[InstituteDocumentMetadata]] = None

class InstituteDocumentMetadata(BaseModel):
    document_type: str = Field(..., description="Type of document")
    description: Optional[str] = Field(None, description="Document description")
```

## Endpoints

### 1. Profile Update (JSON Only)
```
PUT /api/institutes/profile
Content-Type: application/json
```

**Request:**
```json
{
  "institute_name": "Example University",
  "description": "A leading educational institution",
  "city": "Academic City",
  "institute_type": "university"
}
```

### 2. Profile Update with Documents
```
PUT /api/institutes/profile/with-documents
Content-Type: application/json
```

**Request:**
```json
{
  "institute_name": "Example University", 
  "description": "A leading educational institution",
  "city": "Academic City",
  "institute_type": "university",
  "document_metadata": [
    {
      "document_type": "accreditation",
      "description": "University accreditation certificate"
    }
  ]
}
```

**Files:** Separate file uploads for documents

## Benefits

### ✅ Clean Separation of Concerns
- **Routes**: Handle HTTP only
- **CRUD**: Handle business logic only  
- **Schemas**: Handle data validation only

### ✅ Maintainable Code
- Easy to test individual components
- Clear responsibility boundaries
- Reusable CRUD operations

### ✅ Type Safety
- Full Pydantic validation
- IDE autocomplete support
- Compile-time error detection

### ✅ Scalable Architecture
- Easy to add new endpoints
- Business logic reusable across routes
- Clean dependency injection

## Testing

Each layer can be tested independently:

```python
# Test CRUD operation directly
result = await update_institute_profile_with_documents(
    db=test_db,
    institute_id=test_id,
    profile_update=test_profile,
    document_files=test_files,
    document_metadata=test_metadata
)

# Test route with HTTP client
response = client.put("/api/institutes/profile", json=test_data)
```

## Migration from Old Code

**Before (Messy):**
```python
@router.put("/profile/with-documents")
async def update_profile(
    institute_name: str = Form(None),
    description: str = Form(None),
    # ... 20+ individual Form parameters
):
    # 100+ lines of mixed HTTP and business logic
```

**After (Clean):**
```python
@router.put("/profile/with-documents")
async def update_profile(
    request: InstituteProfileWithDocumentsRequest,  # ← Pydantic model
    document_files: Optional[List[UploadFile]] = File(None),
    # ... dependencies
):
    # Extract data and call CRUD
    return await update_institute_profile_with_documents(...)  # ← 5 lines
```

This implementation follows FastAPI best practices and clean architecture principles.
