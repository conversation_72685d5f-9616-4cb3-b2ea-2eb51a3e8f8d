# Frontend API Examples for Institute Document Upload

## Complete Request Body Structure

### **Form Data Fields**

#### Profile Information (all optional)
```javascript
const formData = new FormData();

// Basic Institute Information
formData.append('institute_name', 'Your Institute Name');
formData.append('description', 'Description of your institute');
formData.append('address', '123 Main Street');
formData.append('city', 'Your City');
formData.append('state', 'Your State');
formData.append('postal_code', '12345');
formData.append('website', 'https://yourinstitute.edu');
formData.append('established_year', '1990');
formData.append('institute_type', 'university'); // Options: university, college, school, training_center, research_institute, vocational_school, online_platform, other
formData.append('accreditation', 'Your accreditation details');

// Social Media Links
formData.append('linkedin_url', 'https://linkedin.com/company/yourinstitute');
formData.append('facebook_url', 'https://facebook.com/yourinstitute');
formData.append('twitter_url', 'https://twitter.com/yourinstitute');

// Branding
formData.append('logo_url', 'https://yourdomain.com/logo.png');
formData.append('banner_url', 'https://yourdomain.com/banner.png');
```

#### Document Upload Fields
```javascript
// Document files (File objects from input[type="file"])
formData.append('document_files', file1); // First document file
formData.append('document_files', file2); // Second document file
formData.append('document_files', file3); // Third document file

// Document types (must match the number of files)
formData.append('document_types', 'accreditation'); // For file1
formData.append('document_types', 'license');       // For file2
formData.append('document_types', 'certificate');   // For file3

// Document descriptions (optional, but if provided must match number of files)
formData.append('document_descriptions', 'University accreditation certificate');
formData.append('document_descriptions', 'State operating license');
formData.append('document_descriptions', 'ISO certification');
```

## Complete JavaScript Example

```javascript
async function updateInstituteProfile() {
    const formData = new FormData();
    
    // Get form elements
    const instituteNameInput = document.getElementById('institute_name');
    const descriptionInput = document.getElementById('description');
    const cityInput = document.getElementById('city');
    const stateInput = document.getElementById('state');
    const instituteTypeSelect = document.getElementById('institute_type');
    const establishedYearInput = document.getElementById('established_year');
    const documentFilesInput = document.getElementById('document_files'); // multiple files input
    
    // Add profile data (only if fields have values)
    if (instituteNameInput.value) {
        formData.append('institute_name', instituteNameInput.value);
    }
    if (descriptionInput.value) {
        formData.append('description', descriptionInput.value);
    }
    if (cityInput.value) {
        formData.append('city', cityInput.value);
    }
    if (stateInput.value) {
        formData.append('state', stateInput.value);
    }
    if (instituteTypeSelect.value) {
        formData.append('institute_type', instituteTypeSelect.value);
    }
    if (establishedYearInput.value) {
        formData.append('established_year', establishedYearInput.value);
    }
    
    // Add documents if files are selected
    if (documentFilesInput.files.length > 0) {
        // Document types corresponding to each file
        const documentTypes = ['accreditation', 'license', 'certificate'];
        const documentDescriptions = [
            'University accreditation certificate',
            'State operating license', 
            'Quality certification'
        ];
        
        // Add each file
        for (let i = 0; i < documentFilesInput.files.length; i++) {
            const file = documentFilesInput.files[i];
            formData.append('document_files', file);
            
            // Add corresponding type and description
            if (i < documentTypes.length) {
                formData.append('document_types', documentTypes[i]);
            } else {
                formData.append('document_types', 'other');
            }
            
            if (i < documentDescriptions.length) {
                formData.append('document_descriptions', documentDescriptions[i]);
            }
        }
    }
    
    try {
        const response = await fetch('http://localhost:8000/api/institutes/profile/with-documents', {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('authToken')}` // Your auth token
            },
            body: formData
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('Success:', result);
            alert('Profile updated successfully!');
        } else {
            const error = await response.json();
            console.error('Error:', error);
            alert(`Error: ${error.detail || 'Failed to update profile'}`);
        }
    } catch (error) {
        console.error('Network error:', error);
        alert('Network error occurred');
    }
}
```

## HTML Form Example

```html
<form id="instituteProfileForm" enctype="multipart/form-data">
    <!-- Basic Information -->
    <div>
        <label for="institute_name">Institute Name:</label>
        <input type="text" id="institute_name" name="institute_name">
    </div>
    
    <div>
        <label for="description">Description:</label>
        <textarea id="description" name="description"></textarea>
    </div>
    
    <div>
        <label for="city">City:</label>
        <input type="text" id="city" name="city">
    </div>
    
    <div>
        <label for="state">State:</label>
        <input type="text" id="state" name="state">
    </div>
    
    <div>
        <label for="institute_type">Institute Type:</label>
        <select id="institute_type" name="institute_type">
            <option value="">Select Type</option>
            <option value="university">University</option>
            <option value="college">College</option>
            <option value="school">School</option>
            <option value="training_center">Training Center</option>
            <option value="research_institute">Research Institute</option>
            <option value="vocational_school">Vocational School</option>
            <option value="online_platform">Online Platform</option>
            <option value="other">Other</option>
        </select>
    </div>
    
    <div>
        <label for="established_year">Established Year:</label>
        <input type="number" id="established_year" name="established_year" min="1800" max="2024">
    </div>
    
    <!-- Document Upload -->
    <div>
        <label for="document_files">Upload Documents:</label>
        <input type="file" id="document_files" name="document_files" multiple accept=".pdf,.doc,.docx,.txt,.rtf,.odt">
        <small>Accepted formats: PDF, DOC, DOCX, TXT, RTF, ODT (Max 20MB each)</small>
    </div>
    
    <button type="button" onclick="updateInstituteProfile()">Update Profile</button>
</form>
```

## React Example

```jsx
import React, { useState } from 'react';

function InstituteProfileForm() {
    const [formData, setFormData] = useState({
        institute_name: '',
        description: '',
        city: '',
        state: '',
        institute_type: '',
        established_year: ''
    });
    const [documents, setDocuments] = useState([]);
    
    const handleInputChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };
    
    const handleFileChange = (e) => {
        setDocuments(Array.from(e.target.files));
    };
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        const submitData = new FormData();
        
        // Add form fields
        Object.keys(formData).forEach(key => {
            if (formData[key]) {
                submitData.append(key, formData[key]);
            }
        });
        
        // Add documents
        documents.forEach((file, index) => {
            submitData.append('document_files', file);
            submitData.append('document_types', 'other'); // or determine type based on file
            submitData.append('document_descriptions', `Document ${index + 1}`);
        });
        
        try {
            const response = await fetch('http://localhost:8000/api/institutes/profile/with-documents', {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: submitData
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log('Success:', result);
            } else {
                console.error('Error:', await response.json());
            }
        } catch (error) {
            console.error('Error:', error);
        }
    };
    
    return (
        <form onSubmit={handleSubmit}>
            <input
                type="text"
                name="institute_name"
                placeholder="Institute Name"
                value={formData.institute_name}
                onChange={handleInputChange}
            />
            
            <textarea
                name="description"
                placeholder="Description"
                value={formData.description}
                onChange={handleInputChange}
            />
            
            <input
                type="text"
                name="city"
                placeholder="City"
                value={formData.city}
                onChange={handleInputChange}
            />
            
            <select
                name="institute_type"
                value={formData.institute_type}
                onChange={handleInputChange}
            >
                <option value="">Select Type</option>
                <option value="university">University</option>
                <option value="college">College</option>
                <option value="school">School</option>
                {/* Add other options */}
            </select>
            
            <input
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.txt,.rtf,.odt"
                onChange={handleFileChange}
            />
            
            <button type="submit">Update Profile</button>
        </form>
    );
}
```

## Important Notes

1. **Document Types**: Must be one of: `accreditation`, `license`, `certificate`, `other`
2. **File Formats**: Only PDF, DOC, DOCX, TXT, RTF, ODT are allowed
3. **File Size**: Maximum 20MB per file
4. **Array Matching**: Number of `document_files`, `document_types`, and `document_descriptions` must match
5. **All Fields Optional**: You can update only the fields you want to change
6. **Authentication**: Must include valid Bearer token for institute user
