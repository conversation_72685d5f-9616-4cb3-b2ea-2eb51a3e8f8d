from pydantic import BaseModel, <PERSON>, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum
from fastapi import Form, File, UploadFile
import re


class InstituteTypeEnum(str, Enum):
    UNIVERSITY = "university"
    COLLEGE = "college"
    SCHOOL = "school"
    TRAINING_CENTER = "training_center"
    RESEARCH_INSTITUTE = "research_institute"
    VOCATIONAL_SCHOOL = "vocational_school"
    ONLINE_PLATFORM = "online_platform"
    OTHER = "other"


class VerificationStatusEnum(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    UNDER_REVIEW = "under_review"


# Document upload schemas (defined early for forward references)
class InstituteDocumentUpload(BaseModel):
    document_type: str = Field(..., description="Type of document")
    document_url: str = Field(..., description="URL of uploaded document")
    document_name: str = Field(..., description="Name of the document")
    description: Optional[str] = Field(None, description="Document description")


class InstituteDocumentMetadata(BaseModel):
    """Schema for document metadata in file uploads"""
    document_type: str = Field(..., description="Type of document (accreditation, license, certificate, other)")
    description: Optional[str] = Field(None, description="Document description")


class InstituteProfileWithDocumentsRequest:
    """Form data handler for profile updates with document uploads"""

    def __init__(
        self,
        # Profile fields
        institute_name: Optional[str] = Form(None),
        description: Optional[str] = Form(None),
        address: Optional[str] = Form(None),
        city: Optional[str] = Form(None),
        state: Optional[str] = Form(None),
        postal_code: Optional[str] = Form(None),
        website: Optional[str] = Form(None),
        established_year: Optional[int] = Form(None),
        institute_type: Optional[str] = Form(None),
        accreditation: Optional[str] = Form(None),
        linkedin_url: Optional[str] = Form(None),
        facebook_url: Optional[str] = Form(None),
        twitter_url: Optional[str] = Form(None),
        logo_url: Optional[str] = Form(None),
        banner_url: Optional[str] = Form(None),

        # Document uploads
        document_files: Optional[List[UploadFile]] = File(None),
        document_types: Optional[List[str]] = Form(None),
        document_descriptions: Optional[List[str]] = Form(None),
    ):
        # Profile fields
        self.institute_name = institute_name
        self.description = description
        self.address = address
        self.city = city
        self.state = state
        self.postal_code = postal_code
        self.website = website
        self.established_year = established_year
        self.institute_type = institute_type
        self.accreditation = accreditation
        self.linkedin_url = linkedin_url
        self.facebook_url = facebook_url
        self.twitter_url = twitter_url
        self.logo_url = logo_url
        self.banner_url = banner_url

        # Document fields
        self.document_files = document_files or []
        self.document_types = document_types or []
        self.document_descriptions = document_descriptions or []

    def get_profile_data(self) -> dict:
        """Extract profile data as dictionary"""
        return {
            "institute_name": self.institute_name,
            "description": self.description,
            "address": self.address,
            "city": self.city,
            "state": self.state,
            "postal_code": self.postal_code,
            "website": self.website,
            "established_year": self.established_year,
            "institute_type": self.institute_type,
            "accreditation": self.accreditation,
            "linkedin_url": self.linkedin_url,
            "facebook_url": self.facebook_url,
            "twitter_url": self.twitter_url,
            "logo_url": self.logo_url,
            "banner_url": self.banner_url,
        }

    def get_document_metadata(self) -> List[InstituteDocumentMetadata]:
        """Extract document metadata"""
        metadata = []
        for i, doc_type in enumerate(self.document_types):
            description = self.document_descriptions[i] if i < len(self.document_descriptions) else None
            metadata.append(InstituteDocumentMetadata(
                document_type=doc_type,
                description=description
            ))
        return metadata





class InstituteDocumentOut(BaseModel):
    id: UUID
    institute_id: UUID
    document_type: str
    document_url: str
    document_name: str
    description: Optional[str]
    created_at: datetime  # Using created_at from BaseModel instead of uploaded_at
    verified: bool = False
    verified_at: Optional[datetime]
    verified_by: Optional[UUID]

    class Config:
        from_attributes = True


# Base Schemas
class InstituteRegistrationBase(BaseModel):
    # User account details
    username: str = Field(..., min_length=3, max_length=50, description="Unique username")
    email: EmailStr = Field(..., description="Institute email address")
    mobile: str = Field(..., description="Institute contact number")
    password: str = Field(..., min_length=8, description="Account password")
    country: str = Field(..., description="Country")
    
    # Institute profile details
    institute_name: str = Field(..., min_length=2, max_length=200, description="Official institute name")
    description: Optional[str] = Field(None, max_length=2000, description="Institute description")
    address: Optional[str] = Field(None, description="Physical address")
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    
    # Institute details
    website: Optional[str] = Field(None, description="Official website URL")
    established_year: Optional[int] = Field(None, ge=1800, le=2024, description="Year established")
    institute_type: InstituteTypeEnum = Field(..., description="Type of institute")
    accreditation: Optional[str] = Field(None, description="Accreditation details")
    
    # Social media
    linkedin_url: Optional[str] = Field(None, description="LinkedIn profile URL")
    facebook_url: Optional[str] = Field(None, description="Facebook page URL")
    twitter_url: Optional[str] = Field(None, description="Twitter profile URL")

    @validator('username')
    def validate_username(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        # Check for reserved usernames
        reserved = ['admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'test', 'demo']
        if v.lower() in reserved:
            raise ValueError('Username is reserved and cannot be used')
        return v.lower()

    @validator('password')
    def validate_password(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

    @validator('mobile')
    def validate_phone_numbers(cls, v):
        """Validate mobile number format"""
        if v:
            # Remove spaces and common separators
            cleaned = re.sub(r'[\s\-\(\)]', '', v)
            # Check if it's a valid international format
            if not re.match(r'^\+?[1-9]\d{1,14}$', cleaned):
                raise ValueError('Invalid phone number format. Use international format with country code.')
        return v

    @validator('institute_name')
    def validate_institute_name(cls, v):
        """Validate institute name"""
        if not v or v.strip() == "":
            raise ValueError('Institute name cannot be empty')

        # Check minimum meaningful length
        if len(v.strip()) < 3:
            raise ValueError('Institute name must be at least 3 characters long')

        # Check for inappropriate content (basic check)
        inappropriate_words = ['test', 'fake', 'dummy', 'sample', 'example', 'demo']
        if any(word in v.lower() for word in inappropriate_words):
            raise ValueError('Institute name appears to be a test/fake name')

        # Check for excessive special characters
        if len(re.sub(r'[a-zA-Z0-9\s]', '', v)) > len(v) * 0.3:
            raise ValueError('Institute name contains too many special characters')

        return v.strip()

    @validator('website', 'linkedin_url', 'facebook_url', 'twitter_url')
    def validate_urls(cls, v):
        """Validate URL format and add https if missing"""
        if v:
            v = v.strip()
            if not v.startswith(('http://', 'https://')):
                v = f'https://{v}'

            # Basic URL validation
            url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
            if not re.match(url_pattern, v):
                raise ValueError('Invalid URL format')
        return v

    @validator('linkedin_url')
    def validate_linkedin_url(cls, v):
        """Validate LinkedIn URL specifically"""
        if v and 'linkedin.com' not in v.lower():
            raise ValueError('LinkedIn URL must contain linkedin.com')
        return v

    @validator('facebook_url')
    def validate_facebook_url(cls, v):
        """Validate Facebook URL specifically"""
        if v and 'facebook.com' not in v.lower():
            raise ValueError('Facebook URL must contain facebook.com')
        return v

    @validator('twitter_url')
    def validate_twitter_url(cls, v):
        """Validate Twitter URL specifically"""
        if v and not any(domain in v.lower() for domain in ['twitter.com', 'x.com']):
            raise ValueError('Twitter URL must contain twitter.com or x.com')
        return v

    @validator('postal_code')
    def validate_postal_code(cls, v):
        """Validate postal code format"""
        if v:
            # Allow alphanumeric with spaces and hyphens
            if not re.match(r'^[A-Za-z0-9\s\-]+$', v):
                raise ValueError('Postal code can only contain letters, numbers, spaces, and hyphens')
        return v

    @validator('established_year')
    def validate_established_year(cls, v):
        """Validate established year"""
        if v:
            current_year = datetime.now().year
            if v > current_year:
                raise ValueError('Established year cannot be in the future')
            if v < 1800:
                raise ValueError('Established year seems too old')
        return v


class InstituteProfileUpdate(BaseModel):
    institute_name: Optional[str] = Field(None, min_length=2, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    address: Optional[str] = None
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    website: Optional[str] = None
    established_year: Optional[int] = Field(None, ge=1800, le=2024)
    institute_type: Optional[InstituteTypeEnum] = None
    accreditation: Optional[str] = None
    linkedin_url: Optional[str] = None
    facebook_url: Optional[str] = None
    twitter_url: Optional[str] = None
    logo_url: Optional[str] = None
    banner_url: Optional[str] = None

    # Document upload fields
    documents: Optional[List[InstituteDocumentUpload]] = Field(None, description="List of documents to upload")





class InstituteVerificationUpdate(BaseModel):
    verification_status: VerificationStatusEnum = Field(..., description="New verification status")
    verification_notes: Optional[str] = Field(None, max_length=1000, description="Admin notes")


class InstituteProfileStatus(BaseModel):
    """Clean schema for profile status - data only, no UI logic"""
    profile_exists: bool
    profile_complete: bool
    verification_status: str
    is_verified: bool
    can_submit_for_verification: bool
    required_fields: List[str]
    missing_fields: List[str]
    completion_percentage: float
    verified_at: Optional[datetime] = None
    verification_notes: Optional[str] = None

    class Config:
        from_attributes = True


class InstituteProfileValidation(BaseModel):
    """Schema for validating profile completeness before verification submission"""

    @staticmethod
    def get_required_fields() -> List[str]:
        """Get list of required fields for verification"""
        return [
            "institute_name",
            "description",
            "address",
            "city",
            "state",
            "established_year",
            "institute_type"
        ]

    @staticmethod
    def validate_profile_completeness(profile_data: dict) -> Dict[str, Any]:
        """Validate if profile is complete for verification submission"""
        required_fields = InstituteProfileValidation.get_required_fields()
        missing_fields = []

        for field in required_fields:
            value = profile_data.get(field)
            if not value or (isinstance(value, str) and value.strip() == ""):
                missing_fields.append(field)

        is_complete = len(missing_fields) == 0
        completion_percentage = round((len(required_fields) - len(missing_fields)) / len(required_fields) * 100, 1)

        return {
            "is_complete": is_complete,
            "missing_fields": missing_fields,
            "completion_percentage": completion_percentage,
            "required_fields": required_fields
        }


# Output Schemas
class InstituteProfileOut(BaseModel):
    id: UUID
    user_id: UUID
    institute_name: str
    description: Optional[str]
    address: Optional[str]
    city: Optional[str]
    state: Optional[str]
    postal_code: Optional[str]
    website: Optional[str]
    established_year: Optional[int]
    institute_type: Optional[str]
    accreditation: Optional[str]
    is_verified: bool
    verification_status: str
    verification_notes: Optional[str]
    verified_at: Optional[datetime]
    linkedin_url: Optional[str]
    facebook_url: Optional[str]
    twitter_url: Optional[str]
    logo_url: Optional[str]
    banner_url: Optional[str]
    created_at: datetime
    updated_at: datetime

    # Document information
    documents: Optional[List[InstituteDocumentOut]] = Field(default=[], description="List of uploaded documents")

    class Config:
        from_attributes = True


class InstituteUserOut(BaseModel):
    id: UUID
    username: str
    email: str
    mobile: str
    country: str
    profile_picture: Optional[str]
    user_type: str
    is_email_verified: bool
    is_mobile_verified: bool
    created_at: datetime
    institute_profile: Optional[InstituteProfileOut]

    class Config:
        from_attributes = True


class InstituteDetailedOut(BaseModel):
    user: InstituteUserOut
    profile: InstituteProfileOut
    total_competitions: int = 0
    total_mentors: int = 0
    active_competitions: int = 0
    verification_status: str

    class Config:
        from_attributes = True


# Lightweight schema for admin list views
class InstituteListOut(BaseModel):
    id: UUID
    username: str
    institute_name: str
    city: Optional[str]
    state: Optional[str]
    country: Optional[str]
    institute_type: Optional[str]
    is_verified: bool
    verification_status: str
    profile_picture: Optional[str] = None  # Added profile picture
    created_at: datetime
    updated_at: datetime  # Added to show last activity

    class Config:
        from_attributes = True


# Even more lightweight schema for admin quick lists
class InstituteMinimalOut(BaseModel):
    id: UUID
    institute_name: str
    verification_status: str
    profile_picture: Optional[str] = None
    created_at: datetime
    is_verified: bool

    class Config:
        from_attributes = True


class InstituteListResponse(BaseModel):
    institutes: List[InstituteListOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


class InstituteStatsOut(BaseModel):
    total_institutes: int
    verified_institutes: int
    pending_verification: int
    rejected_institutes: int
    institutes_by_type: Dict[str, int]
    institutes_by_country: Dict[str, int]
    recent_registrations: int  # Last 30 days


# Admin verification schemas - lightweight for lists
class InstituteVerificationListOut(BaseModel):
    id: UUID
    username: str
    institute_name: str
    institute_type: Optional[str]
    country: Optional[str]
    verification_status: str
    profile_picture: Optional[str] = None
    created_at: datetime
    submitted_at: Optional[datetime] = None  # When submitted for verification
    documents_count: int = 0  # Number of documents instead of full list

    class Config:
        from_attributes = True


class InstituteVerificationDetailOut(BaseModel):
    institute: InstituteDetailedOut
    submitted_documents: List[Dict[str, Any]] = []
    verification_history: List[Dict[str, Any]] = []

    class Config:
        from_attributes = True


# Search and filter schemas
class InstituteSearchFilter(BaseModel):
    search: Optional[str] = None
    institute_type: Optional[InstituteTypeEnum] = None
    country: Optional[str] = None
    state: Optional[str] = None
    city: Optional[str] = None
    verification_status: Optional[VerificationStatusEnum] = None
    is_verified: Optional[bool] = None
    established_year_from: Optional[int] = None
    established_year_to: Optional[int] = None



