# Institute Endpoints - Proper Pydantic Usage

## Overview

All institute endpoints now use proper Pydantic models for type safety, validation, and clean API design.

## Endpoints

### 1. Profile Update (JSON Only)

**Endpoint:** `PUT /api/institutes/profile`

**Content-Type:** `application/json`

**Request Body:** Uses `InstituteProfileUpdate` Pydantic model

```json
{
  "institute_name": "Example University",
  "description": "A leading educational institution",
  "address": "123 Education Street",
  "city": "Academic City",
  "state": "Knowledge State",
  "postal_code": "12345",
  "website": "https://example.university.edu",
  "established_year": 1950,
  "institute_type": "university",
  "accreditation": "Accredited by XYZ Board",
  "linkedin_url": "https://linkedin.com/company/example-university",
  "facebook_url": "https://facebook.com/example-university",
  "twitter_url": "https://twitter.com/example_university"
}
```

### 2. Profile Update with Documents

**Endpoint:** `PUT /api/institutes/profile/with-documents`

**Content-Type:** `multipart/form-data`

**Fields:**
- `profile_data` (string): JSON string of `InstituteProfileWithDocumentsRequest`
- `document_files` (files): Array of document files

**Example using curl:**

```bash
curl -X PUT "http://localhost:8000/api/institutes/profile/with-documents" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "profile_data={
    \"institute_name\": \"Example University\",
    \"description\": \"A leading educational institution\",
    \"city\": \"Academic City\",
    \"state\": \"Knowledge State\",
    \"institute_type\": \"university\",
    \"established_year\": 1950,
    \"document_metadata\": [
      {
        \"document_type\": \"accreditation\",
        \"description\": \"University accreditation certificate\"
      },
      {
        \"document_type\": \"license\",
        \"description\": \"Operating license\"
      }
    ]
  }" \
  -F "document_files=@accreditation.pdf" \
  -F "document_files=@license.pdf"
```

**Example using JavaScript/FormData:**

```javascript
const formData = new FormData();

// Profile data as JSON string (Pydantic model)
const profileData = {
  institute_name: "Example University",
  description: "A leading educational institution",
  city: "Academic City",
  state: "Knowledge State",
  institute_type: "university",
  established_year: 1950,
  document_metadata: [
    {
      document_type: "accreditation",
      description: "University accreditation certificate"
    },
    {
      document_type: "license", 
      description: "Operating license"
    }
  ]
};

formData.append('profile_data', JSON.stringify(profileData));
formData.append('document_files', accreditationFile);
formData.append('document_files', licenseFile);

fetch('/api/institutes/profile/with-documents', {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

## Pydantic Models Used

### InstituteProfileUpdate
- Used for JSON-only profile updates
- All fields are optional
- Includes validation rules (min/max lengths, year ranges, etc.)

### InstituteProfileWithDocumentsRequest
- Used for profile updates with document uploads
- Extends InstituteProfileUpdate with document_metadata field
- Validates document metadata structure

### InstituteDocumentMetadata
- Used for document metadata in file uploads
- Fields: document_type, description
- Validates document types (accreditation, license, certificate, other)

## Benefits of Pydantic Models

✅ **Type Safety**: Automatic type checking and conversion
✅ **Validation**: Built-in field validation (lengths, ranges, formats)
✅ **Documentation**: Automatic OpenAPI/Swagger documentation
✅ **IDE Support**: Better autocomplete and error detection
✅ **Maintainability**: Clean, readable code structure
✅ **Error Messages**: Clear validation error messages

## Document Types

Supported document types:
- `accreditation`: Accreditation certificates
- `license`: Operating licenses  
- `certificate`: Other certificates
- `other`: Other document types

## File Restrictions

- **Allowed formats**: PDF, DOC, DOCX, TXT, RTF, ODT
- **Maximum file size**: 20MB per file
- **Storage location**: `uploads/institute_documents/{type}/`

## Response

Both endpoints return `InstituteDetailedOut` which includes:
- Complete user information
- Full profile details
- Document objects (with all fields: id, type, url, name, description, verification status, etc.)
- Statistics (competitions, mentors, etc.)
