from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc
from uuid import UUID
from datetime import datetime, timezone

# Import CRUD functions
from Cruds.Institute.Institute import (
    register_institute, get_institute_by_id, get_institutes, update_institute_profile,
    verify_institute, get_institutes_pending_verification, get_institute_statistics,
    delete_institute, update_institute_profile_with_documents
)

# Import Schemas
from Schemas.Institute.Institute import (
    InstituteRegistrationBase, InstituteProfileUpdate, InstituteVerificationUpdate,
    InstituteUserOut, InstituteDetailedOut, InstituteListResponse,
    InstituteSearchFilter, InstituteStatsOut, InstituteVerificationListOut,
    InstituteMinimalOut, InstituteProfileWithDocumentsRequest
)

# Import dependencies
from Models.users import InstituteProfile, User, UserTypeEnum
from Models.Events import EventStatusEnum
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type, require_institute_with_profile

router = APIRouter()


# Public Institute Routes
@router.post("/register", response_model=InstituteUserOut)
def register_institute_route(
    institute_data: InstituteRegistrationBase,
    db: Session = Depends(get_db)
):
    """Register a new institute"""
    return register_institute(db, institute_data)


@router.get("/public", response_model=InstituteListResponse)
def get_public_institutes(
    search: Optional[str] = Query(None, description="Search term"),
    institute_type: Optional[str] = Query(None, description="Filter by institute type"),
    country: Optional[str] = Query(None, description="Filter by country"),
    state: Optional[str] = Query(None, description="Filter by state"),
    city: Optional[str] = Query(None, description="Filter by city"),
    verified_only: bool = Query(True, description="Show only verified institutes"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get public institutes list"""
    filters = InstituteSearchFilter(
        search=search,
        institute_type=institute_type,
        country=country,
        state=state,
        city=city,
        is_verified=verified_only
    )
    
    skip = (page - 1) * size
    return get_institutes(db, filters, skip, size)


@router.get("/public/{institute_id}", response_model=InstituteDetailedOut)
def get_public_institute(
    institute_id: UUID,
    db: Session = Depends(get_db)
):
    """Get public institute details"""
    return get_institute_by_id(db, institute_id)


# Authenticated Institute Routes
@router.get("/profile", response_model=InstituteDetailedOut)
def get_my_institute_profile(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get current institute's profile with action guidance"""
    current_user = get_current_user(token, db)

    try:
        return get_institute_by_id(db, current_user.id)
    except HTTPException as e:
        if "profile not found" in str(e.detail).lower():
            # Profile doesn't exist - guide user to create it
            raise HTTPException(
                status_code=200,
                detail={
                    "error": "PROFILE_NOT_CREATED",
                    "message": "Your institute profile hasn't been created yet.",
                    "action_required": "Create your profile by sending a PUT request to /api/institutes/profile with your institute information.",
                    "next_step": "Use PUT /api/institutes/profile to create and update your profile",
                    "user_id": str(current_user.id),
                    "username": current_user.username,
                    "ui_actions": {
                        "primary_button": {
                            "text": "Create Profile",
                            "action": "create_profile",
                            "endpoint": "PUT /api/institutes/profile/with-documents",
                            "enabled": True
                        },
                        "secondary_button": None,
                        "show_submit_for_approval": False
                    }
                }
            )
        raise e


@router.put("/profile", response_model=InstituteDetailedOut)
def update_my_institute_profile(
    profile_update: InstituteProfileUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Update current institute's profile (creates profile if doesn't exist)"""
    current_user = get_current_user(token, db)
    return update_institute_profile(db, current_user.id, profile_update)


@router.put("/profile/with-documents", response_model=InstituteDetailedOut)
async def update_institute_profile_with_documents_route(
    request: InstituteProfileWithDocumentsRequest,
    document_files: Optional[List[UploadFile]] = File(None),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update institute profile with document uploads.

    Uses proper Pydantic models and CRUD operations.
    """
    current_user = get_current_user(token, db)

    # Extract profile fields (exclude document_metadata)
    profile_fields = request.model_dump(exclude={'document_metadata'})
    profile_update = InstituteProfileUpdate(**profile_fields)

    # Call CRUD operation
    return await update_institute_profile_with_documents(
        db=db,
        institute_id=current_user.id,
        profile_update=profile_update,
        document_files=document_files,
        document_metadata=request.document_metadata
    )





@router.get("/profile/status")
def get_profile_completion_status(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get institute profile completion status, verification details, and available actions"""
    current_user = get_current_user(token, db)

    if not current_user.institute_profile:
        return {
            "profile_exists": False,
            "profile_complete": False,
            "verification_status": "not_created",
            "can_submit_for_verification": False,
            "message": "Institute profile not created yet. Use PUT /api/institutes/profile to create and update your profile.",
            "action_required": "Create your institute profile by sending profile data to PUT /api/institutes/profile",
            "required_fields": [
                "institute_name", "description", "address", "city", "state",
                "established_year", "institute_type"
            ],
            "missing_fields": [
                "institute_name", "description", "address", "city", "state",
                "established_year", "institute_type"
            ],
            "completion_percentage": 0.0,
            # Frontend action guidance
            "ui_actions": {
                "primary_button": {
                    "text": "Create Profile",
                    "action": "create_profile",
                    "endpoint": "PUT /api/institutes/profile/with-documents",
                    "enabled": True
                },
                "secondary_button": None,
                "show_submit_for_approval": False
            }
        }

    profile = current_user.institute_profile

    # Define required fields for verification submission
    required_fields = [
        "institute_name", "description", "address", "city", "state",
        "established_year", "institute_type"
    ]

    # Check which fields are missing
    missing_fields = []
    for field in required_fields:
        value = getattr(profile, field, None)
        if not value or (isinstance(value, str) and value.strip() == ""):
            missing_fields.append(field)

    profile_complete = len(missing_fields) == 0
    can_submit_for_verification = profile_complete and profile.verification_status in ["pending", "rejected"]

    # Determine UI actions based on profile state
    ui_actions = {}

    if profile.verification_status == "verified":
        # Profile is verified - only allow updates
        ui_actions = {
            "primary_button": {
                "text": "Update Profile",
                "action": "update_profile",
                "endpoint": "PUT /api/institutes/profile/with-documents",
                "enabled": True
            },
            "secondary_button": None,
            "show_submit_for_approval": False,
            "status_message": "Your profile is verified and active!"
        }
    elif profile.verification_status == "under_review":
        # Profile is under review - no actions available
        ui_actions = {
            "primary_button": {
                "text": "Update Profile",
                "action": "update_profile",
                "endpoint": "PUT /api/institutes/profile/with-documents",
                "enabled": False,
                "disabled_reason": "Profile is currently under review"
            },
            "secondary_button": None,
            "show_submit_for_approval": False,
            "status_message": "Your profile is currently under review by our team."
        }
    elif profile_complete and profile.verification_status == "pending":
        # Profile is complete but not submitted for verification
        ui_actions = {
            "primary_button": {
                "text": "Update Profile",
                "action": "update_profile",
                "endpoint": "PUT /api/institutes/profile/with-documents",
                "enabled": True
            },
            "secondary_button": {
                "text": "Send for Approval",
                "action": "submit_for_verification",
                "endpoint": "POST /api/institutes/profile/submit-for-verification",
                "enabled": True,
                "style": "success"
            },
            "show_submit_for_approval": True,
            "status_message": "Your profile is complete and ready for verification!"
        }
    elif profile.verification_status == "rejected":
        # Profile was rejected - allow updates and resubmission
        ui_actions = {
            "primary_button": {
                "text": "Update Profile",
                "action": "update_profile",
                "endpoint": "PUT /api/institutes/profile/with-documents",
                "enabled": True
            },
            "secondary_button": {
                "text": "Resubmit for Approval",
                "action": "submit_for_verification",
                "endpoint": "POST /api/institutes/profile/submit-for-verification",
                "enabled": profile_complete,
                "style": "warning",
                "disabled_reason": "Complete all required fields first" if not profile_complete else None
            },
            "show_submit_for_approval": True,
            "status_message": f"Profile was rejected: {profile.verification_notes or 'Please review and update your profile.'}"
        }
    else:
        # Profile exists but incomplete
        ui_actions = {
            "primary_button": {
                "text": "Update Profile",
                "action": "update_profile",
                "endpoint": "PUT /api/institutes/profile/with-documents",
                "enabled": True
            },
            "secondary_button": {
                "text": "Send for Approval",
                "action": "submit_for_verification",
                "endpoint": "POST /api/institutes/profile/submit-for-verification",
                "enabled": False,
                "disabled_reason": f"Complete {len(missing_fields)} missing field(s) first",
                "style": "secondary"
            },
            "show_submit_for_approval": False,
            "status_message": f"Complete your profile ({round((len(required_fields) - len(missing_fields)) / len(required_fields) * 100, 1)}% done)"
        }

    return {
        "profile_exists": True,
        "profile_complete": profile_complete,
        "verification_status": profile.verification_status,
        "is_verified": profile.is_verified,
        "verified_at": profile.verified_at,
        "verification_notes": profile.verification_notes,
        "can_submit_for_verification": can_submit_for_verification,
        "required_fields": required_fields,
        "missing_fields": missing_fields,
        "completion_percentage": round((len(required_fields) - len(missing_fields)) / len(required_fields) * 100, 1),
        "ui_actions": ui_actions
    }


@router.post("/profile/submit-for-verification")
def submit_profile_for_verification(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Submit institute profile for admin verification"""
    current_user = get_current_user(token, db)

    # Check if profile exists
    if not current_user.institute_profile:
        raise HTTPException(
            status_code=400,
            detail="Institute profile not found. Please create your profile first using PUT /api/institutes/profile"
        )

    profile = current_user.institute_profile

    # Check if profile is complete
    required_fields = [
        "institute_name", "description", "address", "city", "state",
        "established_year", "institute_type"
    ]

    missing_fields = []
    for field in required_fields:
        value = getattr(profile, field, None)
        if not value or (isinstance(value, str) and value.strip() == ""):
            missing_fields.append(field)

    if missing_fields:
        raise HTTPException(
            status_code=400,
            detail=f"Profile incomplete. Missing fields: {', '.join(missing_fields)}"
        )

    # Check if already submitted or verified
    if profile.verification_status not in ["pending", "rejected"]:
        if profile.verification_status == "approved":
            raise HTTPException(
                status_code=400,
                detail="Institute is already verified"
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Profile verification status: {profile.verification_status}"
            )

    # Update status and set submitted timestamp
    from datetime import datetime, timezone

    if profile.verification_status == "rejected":
        profile.verification_status = "pending"
        profile.verification_notes = None  # Clear previous rejection notes

    # Set submitted timestamp
    profile.submitted_at = datetime.now(timezone.utc)
    profile.verification_status = "under_review"  # Change to under_review when submitted

    db.commit()
    db.refresh(current_user)

    return {
        "message": "Profile submitted for verification successfully",
        "verification_status": "under_review",
        "submitted_at": profile.submitted_at.isoformat(),
        "estimated_review_time": "2-5 business days"
    }


@router.get("/list/minimal", response_model=List[InstituteMinimalOut])
def get_institutes_minimal(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    verification_status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get minimal institute list for admin quick views (super lightweight)"""

    query = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(User.user_type == UserTypeEnum.institute)

    # Filter by verification status if provided
    if verification_status:
        query = query.join(InstituteProfile).filter(
            InstituteProfile.verification_status == verification_status
        )

    institutes = query.order_by(desc(User.created_at)).offset(skip).limit(limit).all()

    minimal_list = []
    for user in institutes:
        if user.institute_profile:
            minimal_list.append(InstituteMinimalOut(
                id=user.id,
                institute_name=user.institute_profile.institute_name,
                verification_status=user.institute_profile.verification_status,
                profile_picture=user.institute_profile.logo_url,
                created_at=user.created_at,
                is_verified=user.institute_profile.is_verified
            ))

    return minimal_list


@router.get("/dashboard")
def get_institute_dashboard(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get institute dashboard with status, statistics, and next actions"""
    current_user = get_current_user(token, db)

    # Get profile status
    profile_status = None
    if current_user.institute_profile:
        profile = current_user.institute_profile
        required_fields = [
            "institute_name", "description", "address", "city", "state",
            "established_year", "institute_type"
        ]

        missing_fields = []
        for field in required_fields:
            value = getattr(profile, field, None)
            if not value or (isinstance(value, str) and value.strip() == ""):
                missing_fields.append(field)

        profile_status = {
            "complete": len(missing_fields) == 0,
            "completion_percentage": round((len(required_fields) - len(missing_fields)) / len(required_fields) * 100, 1),
            "missing_fields": missing_fields,
            "verification_status": profile.verification_status,
            "is_verified": profile.is_verified,
            "verified_at": profile.verified_at,
            "verification_notes": profile.verification_notes
        }

    # Get statistics (only if verified)
    statistics = {
        "total_events": 0,
        "active_events": 0,
        "total_competitions": 0,
        "active_competitions": 0,
        "total_mentors": 0,
        "active_mentors": 0
    }

    if current_user.institute_profile and current_user.institute_profile.is_verified:
        from Models.Events import Event
        from Models.users import MentorInstituteAssociation

        # Count events
        total_events = db.query(Event).filter(
            Event.institute_id == current_user.id
        ).count()

        # Active events (published and currently ongoing)
        now = datetime.now(timezone.utc)
        active_events = db.query(Event).filter(
            Event.institute_id == current_user.id,
            Event.status == EventStatusEnum.PUBLISHED,
            Event.start_datetime <= now,
            Event.end_datetime > now
        ).count()

        # Count competitions
        total_competitions = db.query(Event).filter(
            Event.institute_id == current_user.id,
            Event.is_competition == True
        ).count()

        # Active competitions (published and currently ongoing)
        active_competitions = db.query(Event).filter(
            Event.institute_id == current_user.id,
            Event.is_competition == True,
            Event.status == EventStatusEnum.PUBLISHED,
            Event.start_datetime <= now,
            Event.end_datetime > now
        ).count()

        # Count mentors
        total_mentors = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.institute_id == current_user.id
        ).count()

        active_mentors = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.institute_id == current_user.id,
            MentorInstituteAssociation.status == "active"
        ).count()

        statistics = {
            "total_events": total_events,
            "active_events": active_events,
            "total_competitions": total_competitions,
            "active_competitions": active_competitions,
            "total_mentors": total_mentors,
            "active_mentors": active_mentors
        }

    # Determine next actions
    next_actions = []
    if not current_user.institute_profile:
        next_actions.append({
            "action": "complete_profile",
            "title": "Complete Your Institute Profile",
            "description": "Fill in your institute information to get started",
            "priority": "high",
            "url": "/api/institutes/profile"
        })
    elif profile_status and not profile_status["complete"]:
        next_actions.append({
            "action": "complete_profile",
            "title": "Complete Missing Profile Fields",
            "description": f"Complete {len(profile_status['missing_fields'])} missing fields",
            "priority": "high",
            "url": "/api/institutes/profile"
        })
    elif profile_status and profile_status["verification_status"] == "pending":
        next_actions.append({
            "action": "wait_verification",
            "title": "Verification Pending",
            "description": "Your profile is under review by our admin team",
            "priority": "medium",
            "url": None
        })
    elif profile_status and profile_status["verification_status"] == "rejected":
        next_actions.append({
            "action": "resubmit_profile",
            "title": "Resubmit for Verification",
            "description": "Address the feedback and resubmit your profile",
            "priority": "high",
            "url": "/api/institutes/profile/submit-for-verification"
        })
    elif profile_status and profile_status["is_verified"]:
        if statistics["total_events"] == 0:
            next_actions.append({
                "action": "create_first_event",
                "title": "Create Your First Event",
                "description": "Start engaging with students by creating an event",
                "priority": "medium",
                "url": "/api/institutes/events"
            })
        if statistics["total_mentors"] == 0:
            next_actions.append({
                "action": "invite_mentors",
                "title": "Invite Mentors",
                "description": "Expand your team by inviting qualified mentors",
                "priority": "low",
                "url": "/api/institutes/mentors/invite"
            })

    return {
        "institute_name": current_user.institute_profile.institute_name if current_user.institute_profile else None,
        "profile_status": profile_status,
        "statistics": statistics,
        "next_actions": next_actions,
        "account_created": current_user.created_at,
        "last_login": current_user.updated_at  # Assuming this tracks last activity
    }


@router.get("/{institute_id}", response_model=InstituteDetailedOut)
def get_institute(
    institute_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get institute details (authenticated users)"""
    return get_institute_by_id(db, institute_id)


# Admin Routes
@router.get("/admin/all", response_model=InstituteListResponse)
def get_all_institutes_admin(
    search: Optional[str] = Query(None, description="Search term"),
    institute_type: Optional[str] = Query(None, description="Filter by institute type"),
    country: Optional[str] = Query(None, description="Filter by country"),
    verification_status: Optional[str] = Query(None, description="Filter by verification status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get all institutes (admin only)"""
    filters = InstituteSearchFilter(
        search=search,
        institute_type=institute_type,
        country=country,
        verification_status=verification_status
    )
    
    skip = (page - 1) * size
    return get_institutes(db, filters, skip, size)



@router.get("/admin/pending-verification", response_model=InstituteListResponse)
def get_pending_verification_institutes(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get institutes pending verification (admin only)"""
    return get_institutes_pending_verification(db, skip, limit)

@router.get("/admin/institute/{institute_id}", response_model=InstituteDetailedOut)
def get_institute_admin(
    institute_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get institute details (admin only)"""
    return get_institute_by_id(db, institute_id)


@router.put("/admin/{institute_id}/verify", response_model=InstituteDetailedOut)
def verify_institute_admin(
    institute_id: UUID,
    verification_update: InstituteVerificationUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Verify or reject institute (admin only)"""
    current_user = get_current_user(token, db)
    return verify_institute(db, institute_id, verification_update, current_user.id)


@router.post("/admin/bulk-verify")
def bulk_verify_institutes(
    institute_ids: List[UUID],
    verification_status: str,
    verification_notes: Optional[str] = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Bulk verify or reject multiple institutes (admin only)"""
    current_user = get_current_user(token, db)

    if verification_status not in ["approved", "rejected"]:
        raise HTTPException(status_code=400, detail="Status must be 'approved' or 'rejected'")

    results = []
    errors = []

    for institute_id in institute_ids:
        try:
            verification_update = InstituteVerificationUpdate(
                verification_status=verification_status,
                verification_notes=verification_notes
            )
            result = verify_institute(db, institute_id, verification_update, current_user.id)
            results.append({
                "institute_id": institute_id,
                "status": "success",
                "institute_name": result.profile.institute_name
            })
        except Exception as e:
            errors.append({
                "institute_id": institute_id,
                "status": "error",
                "error": str(e)
            })

    return {
        "message": f"Bulk verification completed",
        "total_processed": len(institute_ids),
        "successful": len(results),
        "failed": len(errors),
        "results": results,
        "errors": errors
    }


@router.get("/admin/verification-queue")
def get_verification_queue(
    status: Optional[str] = Query(None, description="Filter by verification status"),
    institute_type: Optional[str] = Query(None, description="Filter by institute type"),
    country: Optional[str] = Query(None, description="Filter by country"),
    days_pending: Optional[int] = Query(None, description="Filter by days pending"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get detailed verification queue with filtering options (admin only)"""
    from Models.users import InstituteProfile
    from sqlalchemy import and_, func
    from datetime import datetime, timedelta

    query = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.user_type == UserTypeEnum.institute
    ).join(InstituteProfile)

    # Apply filters
    if status:
        query = query.filter(InstituteProfile.verification_status == status)

    if institute_type:
        query = query.filter(InstituteProfile.institute_type == institute_type)

    if country:
        query = query.filter(User.country == country)

    if days_pending:
        cutoff_date = datetime.utcnow() - timedelta(days=days_pending)
        query = query.filter(User.created_at <= cutoff_date)

    # Get total count
    total = query.count()

    # Get paginated results
    institutes = query.order_by(desc(User.created_at)).offset(skip).limit(limit).all()

    verification_queue = []
    for user in institutes:
        profile = user.institute_profile
        days_since_registration = (datetime.utcnow() - user.created_at).days

        verification_queue.append({
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "institute_name": profile.institute_name,
            "institute_type": profile.institute_type,
            "country": user.country,
            "city": profile.city,
            "state": profile.state,
            "verification_status": profile.verification_status,
            "verification_notes": profile.verification_notes,
            "is_verified": profile.is_verified,
            "verified_at": profile.verified_at,
            "created_at": user.created_at,
            "days_since_registration": days_since_registration,
            "profile_completion": {
                "institute_name": bool(profile.institute_name),
                "description": bool(profile.description),
                "address": bool(profile.address),
                "phone": bool(profile.phone),
                "email": bool(profile.email),
                "established_year": bool(profile.established_year),
                "institute_type": bool(profile.institute_type)
            }
        })

    return {
        "total": total,
        "skip": skip,
        "limit": limit,
        "institutes": verification_queue
    }


@router.post("/admin/{institute_id}/repair-profile")
def repair_institute_profile(
    institute_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Repair missing institute profile (admin only - for data integrity issues)"""
    from Models.users import InstituteProfile

    # Check if user exists and is an institute
    user = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not user:
        raise HTTPException(status_code=404, detail="Institute user not found")

    # Check if profile already exists
    if user.institute_profile:
        return {
            "message": "Institute profile already exists",
            "profile_id": str(user.institute_profile.id),
            "status": "no_action_needed"
        }

    # Create minimal institute profile for data repair
    institute_profile = InstituteProfile(
        user_id=user.id,
        institute_name="",  # Must be filled by institute
        description="",
        address="",
        city="",
        state="",
        postal_code="",
        website="",
        established_year=None,
        institute_type="",
        accreditation="",
        linkedin_url="",
        facebook_url="",
        twitter_url="",
        is_verified=False,
        verification_status="pending"  # They need to complete profile first
    )

    db.add(institute_profile)
    db.commit()
    db.refresh(institute_profile)

    return {
        "message": "Institute profile created successfully",
        "profile_id": str(institute_profile.id),
        "status": "profile_created",
        "next_steps": [
            "Institute must complete all required profile fields",
            "Institute must submit profile for verification",
            "Admin must review and approve the profile"
        ]
    }


@router.delete("/admin/{institute_id}")
def delete_institute_admin(
    institute_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Delete institute (admin only)"""
    current_user = get_current_user(token, db)
    delete_institute(db, institute_id, current_user.id)
    return {"message": "Institute deleted successfully"}


@router.get("/admin/statistics", response_model=InstituteStatsOut)
def get_institute_statistics_admin(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get institute statistics (admin only)"""
    return get_institute_statistics(db)


# Institute Search and Discovery
@router.get("/search/advanced", response_model=InstituteListResponse)
def advanced_institute_search(
    search: Optional[str] = Query(None, description="Search term"),
    institute_type: Optional[str] = Query(None, description="Filter by institute type"),
    country: Optional[str] = Query(None, description="Filter by country"),
    state: Optional[str] = Query(None, description="Filter by state"),
    city: Optional[str] = Query(None, description="Filter by city"),
    established_year_from: Optional[int] = Query(None, description="Established year from"),
    established_year_to: Optional[int] = Query(None, description="Established year to"),
    verified_only: bool = Query(True, description="Show only verified institutes"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Advanced institute search"""
    filters = InstituteSearchFilter(
        search=search,
        institute_type=institute_type,
        country=country,
        state=state,
        city=city,
        established_year_from=established_year_from,
        established_year_to=established_year_to,
        is_verified=verified_only
    )
    
    skip = (page - 1) * size
    return get_institutes(db, filters, skip, size)


@router.get("/featured")
def get_featured_institutes(
    limit: int = Query(10, ge=1, le=50, description="Number of featured institutes"),
    db: Session = Depends(get_db)
):
    """Get featured institutes"""
    filters = InstituteSearchFilter(is_verified=True)
    return get_institutes(db, filters, 0, limit)


@router.get("/by-country/{country}")
def get_institutes_by_country(
    country: str,
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get institutes by country"""
    filters = InstituteSearchFilter(country=country, is_verified=True)
    skip = (page - 1) * size
    return get_institutes(db, filters, skip, size)


@router.get("/by-type/{institute_type}")
def get_institutes_by_type(
    institute_type: str,
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get institutes by type"""
    filters = InstituteSearchFilter(institute_type=institute_type, is_verified=True)
    skip = (page - 1) * size
    return get_institutes(db, filters, skip, size)


