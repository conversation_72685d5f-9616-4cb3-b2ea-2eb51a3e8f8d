# Single Body Request - Institute Profile with Documents

## Overview

The institute profile update endpoint now uses a **single multipart form body** that contains both profile data and file uploads together. This is much simpler than having separate JSON and file bodies.

## Endpoint

```
PUT /api/institutes/profile/with-documents
Content-Type: multipart/form-data
```

## Single Body Structure

All data is sent as form fields in one multipart request:

### Profile Fields (Form Data)
- `institute_name` (string, optional)
- `description` (string, optional)  
- `address` (string, optional)
- `city` (string, optional)
- `state` (string, optional)
- `postal_code` (string, optional)
- `website` (string, optional)
- `established_year` (integer, optional)
- `institute_type` (string, optional)
- `accreditation` (string, optional)
- `linkedin_url` (string, optional)
- `facebook_url` (string, optional)
- `twitter_url` (string, optional)
- `logo_url` (string, optional)
- `banner_url` (string, optional)

### Document Fields (Form Data + Files)
- `document_files` (files, optional) - Array of document files
- `document_types` (strings, optional) - Array of document types
- `document_descriptions` (strings, optional) - Array of descriptions

## Usage Examples

### JavaScript/FormData
```javascript
const formData = new FormData();

// Profile fields
formData.append('institute_name', 'Example University');
formData.append('description', 'A leading educational institution');
formData.append('city', 'Academic City');
formData.append('state', 'Knowledge State');
formData.append('institute_type', 'university');
formData.append('established_year', '1950');

// Document uploads
formData.append('document_files', accreditationFile);
formData.append('document_files', licenseFile);
formData.append('document_types', 'accreditation');
formData.append('document_types', 'license');
formData.append('document_descriptions', 'University accreditation certificate');
formData.append('document_descriptions', 'Operating license');

// Send request
fetch('/api/institutes/profile/with-documents', {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

### curl Example
```bash
curl -X PUT "http://localhost:8000/api/institutes/profile/with-documents" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "institute_name=Example University" \
  -F "description=A leading educational institution" \
  -F "city=Academic City" \
  -F "state=Knowledge State" \
  -F "institute_type=university" \
  -F "established_year=1950" \
  -F "document_files=@accreditation.pdf" \
  -F "document_files=@license.pdf" \
  -F "document_types=accreditation" \
  -F "document_types=license" \
  -F "document_descriptions=University accreditation certificate" \
  -F "document_descriptions=Operating license"
```

### Python requests
```python
import requests

files = [
    ('document_files', ('accreditation.pdf', open('accreditation.pdf', 'rb'), 'application/pdf')),
    ('document_files', ('license.pdf', open('license.pdf', 'rb'), 'application/pdf'))
]

data = {
    'institute_name': 'Example University',
    'description': 'A leading educational institution',
    'city': 'Academic City',
    'state': 'Knowledge State',
    'institute_type': 'university',
    'established_year': 1950,
    'document_types': ['accreditation', 'license'],
    'document_descriptions': ['University accreditation certificate', 'Operating license']
}

response = requests.put(
    'http://localhost:8000/api/institutes/profile/with-documents',
    headers={'Authorization': f'Bearer {token}'},
    files=files,
    data=data
)
```

## Benefits

### ✅ **Single Request Body**
- No separate JSON and file bodies
- Everything in one multipart form
- Simpler client implementation

### ✅ **Easy to Use**
- Standard HTML form approach
- Works with any HTTP client
- No complex JSON + file handling

### ✅ **Clean Backend Processing**
- CRUD function extracts what it needs
- Profile data via `request.get_profile_data()`
- Files via `request.document_files`
- Types via `request.document_types`

### ✅ **Flexible**
- Can send profile data only (no files)
- Can send files only (no profile updates)
- Can send both together
- All fields are optional

## Document Types

Supported document types:
- `accreditation` - Accreditation certificates
- `license` - Operating licenses
- `certificate` - Other certificates  
- `other` - Other document types

## Validation

- **File validation**: Automatic file type and size validation
- **Profile validation**: Pydantic model validation in CRUD
- **Document matching**: Files, types, and descriptions arrays must match in length
- **Type validation**: Document types must be from allowed list

## Response

Returns `InstituteDetailedOut` with:
- Updated profile information
- Complete document objects (with all fields)
- User information
- Statistics

This approach is much cleaner and easier to use than having separate request bodies!
